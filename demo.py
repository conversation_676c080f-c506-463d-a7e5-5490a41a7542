#!/usr/bin/env python3
"""
RestAgent 演示脚本
用于验证核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config():
    """测试配置模块"""
    print("🔧 测试配置模块...")
    try:
        from src.config.settings import get_settings
        settings = get_settings()
        print(f"✅ 配置加载成功")
        print(f"   YApi地址: {settings.yapi.base_url}")
        print(f"   大模型提供商: {settings.llm.provider}")
        print(f"   向量数据库类型: {settings.vector_db.type}")
        return True
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        return False

def test_vector_db():
    """测试向量数据库模块"""
    print("\n🗄️ 测试向量数据库模块...")
    try:
        from src.vector_db.manager import VectorDBManager
        
        # 创建管理器实例
        manager = VectorDBManager()
        print("✅ 向量数据库管理器创建成功")
        
        # 获取数据库信息
        info = manager.get_database_info()
        print(f"   数据库状态: {info.get('status', 'unknown')}")
        print(f"   API数量: {info.get('api_count', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ 向量数据库模块测试失败: {e}")
        return False

def test_yapi_client():
    """测试YApi客户端模块"""
    print("\n🔌 测试YApi客户端模块...")
    try:
        from src.yapi_client.manager import YApiManager
        from src.yapi_client.parser import YApiParser
        
        # 创建管理器实例
        manager = YApiManager()
        print("✅ YApi管理器创建成功")
        
        # 测试解析器
        parser = YApiParser()
        sample_api = {
            "_id": "test_123",
            "title": "测试API",
            "path": "/api/test",
            "method": "GET",
            "desc": "这是一个测试API"
        }
        
        parsed = parser.parse_interface(sample_api)
        print("✅ API数据解析成功")
        print(f"   解析后标题: {parsed.get('title')}")
        
        return True
    except Exception as e:
        print(f"❌ YApi客户端模块测试失败: {e}")
        return False

def test_agent():
    """测试智能助手模块"""
    print("\n🤖 测试智能助手模块...")
    try:
        from src.agent.agent import RestAgent
        
        # 创建Agent实例
        agent = RestAgent()
        print("✅ RestAgent创建成功")
        
        # 测试数据库信息获取
        db_info = agent.get_database_info()
        print(f"   数据库状态: {db_info.get('status', 'unknown')}")
        
        return True
    except Exception as e:
        print(f"❌ 智能助手模块测试失败: {e}")
        return False

def test_web_ui():
    """测试Web UI模块"""
    print("\n🌐 测试Web UI模块...")
    try:
        from src.web_ui.app import create_app
        
        # 创建FastAPI应用
        app = create_app()
        print("✅ FastAPI应用创建成功")
        print(f"   应用标题: {app.title}")
        print(f"   应用版本: {app.version}")
        
        return True
    except Exception as e:
        print(f"❌ Web UI模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 RestAgent 功能演示")
    print("=" * 50)
    
    # 检查必要的目录
    required_dirs = ['logs', 'data', 'data/chroma_db']
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"📁 创建目录: {dir_path}")
    
    # 运行测试
    tests = [
        test_config,
        test_vector_db,
        test_yapi_client,
        test_agent,
        test_web_ui
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心模块测试通过！")
        print("\n📝 下一步操作:")
        print("1. 编辑 config.yaml 配置文件")
        print("2. 复制 .env.example 为 .env 并填入配置")
        print("3. 运行 python run.py 启动服务")
        print("4. 访问 http://localhost:8000 使用应用")
    else:
        print("⚠️ 部分模块测试失败，请检查配置和依赖")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
