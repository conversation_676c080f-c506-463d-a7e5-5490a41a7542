# RestAgent 开发指南

## 项目结构

```
restAgent/
├── src/                    # 源代码目录
│   ├── config/            # 配置管理
│   │   ├── settings.py    # 配置类定义
│   │   └── logger.py      # 日志配置
│   ├── vector_db/         # 向量数据库模块
│   │   ├── chroma_client.py    # ChromaDB客户端
│   │   ├── embedding_client.py # 嵌入模型客户端
│   │   └── manager.py     # 向量数据库管理器
│   ├── yapi_client/       # YApi客户端模块
│   │   ├── client.py      # YApi API客户端
│   │   ├── parser.py      # 数据解析器
│   │   └── manager.py     # YApi管理器
│   ├── agent/             # 智能助手核心
│   │   ├── agent.py       # 主要Agent类
│   │   ├── llm_client.py  # 大模型客户端
│   │   └── api_caller.py  # API调用器
│   └── web_ui/            # Web界面
│       ├── app.py         # FastAPI应用
│       └── routers/       # 路由模块
├── templates/             # HTML模板
├── static/               # 静态资源
├── tests/                # 测试文件
├── debug/                # 调试文件目录
├── logs/                 # 日志文件目录
├── data/                 # 数据存储目录
├── config.yaml           # 主配置文件
├── requirements.txt      # Python依赖
├── main.py              # 主入口文件
└── run.py               # 启动脚本
```

## 开发环境设置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制并编辑配置文件：

```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置信息

# 编辑 config.yaml 文件，配置各个服务的参数
```

### 3. 启动开发服务器

```bash
# 使用默认配置启动
python run.py

# 启用热重载
python run.py --reload

# 指定端口
python run.py --port 8080
```

## 核心模块说明

### 1. 配置管理 (src/config/)

- `settings.py`: 使用Pydantic定义配置模型，支持从YAML文件和环境变量加载配置
- `logger.py`: 配置loguru日志系统

### 2. 向量数据库 (src/vector_db/)

- `chroma_client.py`: ChromaDB客户端，处理向量存储和搜索
- `embedding_client.py`: 嵌入模型客户端，支持OpenAI等提供商
- `manager.py`: 向量数据库管理器，提供高级API

### 3. YApi客户端 (src/yapi_client/)

- `client.py`: YApi REST API客户端
- `parser.py`: API数据解析和格式化
- `manager.py`: YApi数据管理器

### 4. 智能助手 (src/agent/)

- `agent.py`: 主要的Agent类，实现对话流程
- `llm_client.py`: 大模型客户端，支持工具调用
- `api_caller.py`: REST API调用器

### 5. Web界面 (src/web_ui/)

- `app.py`: FastAPI应用配置
- `routers/`: 各功能模块的路由定义

## 开发规范

### 1. 代码风格

- 使用Python 3.8+语法
- 遵循PEP 8代码规范
- 使用类型注解
- 添加适当的文档字符串

### 2. 错误处理

- 使用try-except捕获异常
- 记录详细的错误日志
- 返回友好的错误信息

### 3. 日志记录

```python
from loguru import logger

logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")
logger.debug("调试日志")
```

### 4. 配置管理

```python
from src.config.settings import get_settings

settings = get_settings()
api_key = settings.llm.api_key
```

## 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_config.py

# 运行并显示覆盖率
pytest --cov=src

# 运行特定标记的测试
pytest -m unit
```

### 编写测试

- 测试文件以 `test_` 开头
- 测试类以 `Test` 开头
- 测试方法以 `test_` 开头
- 使用pytest fixtures管理测试数据
- 使用mock模拟外部依赖

## 部署

### 1. 生产环境配置

- 设置正确的环境变量
- 配置生产数据库
- 设置日志级别为INFO或WARNING
- 禁用调试模式

### 2. Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run.py", "--host", "0.0.0.0"]
```

### 3. 系统服务

创建systemd服务文件：

```ini
[Unit]
Description=RestAgent Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/restAgent
ExecStart=/path/to/python /path/to/restAgent/run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 故障排除

### 1. 常见问题

- **ChromaDB连接失败**: 检查数据目录权限
- **YApi连接失败**: 验证URL和认证信息
- **大模型调用失败**: 检查API密钥和网络连接
- **端口占用**: 使用 `--port` 参数指定其他端口

### 2. 调试技巧

- 查看日志文件 `logs/app.log`
- 使用 `--reload` 参数启用热重载
- 在代码中添加 `logger.debug()` 输出调试信息
- 使用浏览器开发者工具检查前端问题

### 3. 性能优化

- 调整向量数据库的批处理大小
- 优化大模型的参数设置
- 使用缓存减少重复计算
- 监控内存和CPU使用情况

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 等待代码审查

## 许可证

MIT License
