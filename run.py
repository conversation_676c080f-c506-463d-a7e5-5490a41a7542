#!/usr/bin/env python3
"""
RestAgent 启动脚本
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    parser = argparse.ArgumentParser(description='RestAgent - REST API智能助手')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--reload', action='store_true', help='启用热重载')
    parser.add_argument('--config', default='config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"警告: 配置文件 {args.config} 不存在，将使用默认配置")
        print("请复制 config.yaml 文件并修改相关配置")
    
    # 检查环境变量文件
    if not os.path.exists('.env'):
        print("警告: .env 文件不存在")
        print("请复制 .env.example 为 .env 并填入相关配置")
    
    # 启动应用
    try:
        import uvicorn
        from src.web_ui.app import create_app
        
        app = create_app()
        
        print(f"启动 RestAgent 服务...")
        print(f"访问地址: http://{args.host}:{args.port}")
        print(f"配置文件: {args.config}")
        print("按 Ctrl+C 停止服务")
        
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            reload=args.reload
        )
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
