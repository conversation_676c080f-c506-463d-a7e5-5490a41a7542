# RestAgent API 文档

## 概述

RestAgent提供RESTful API接口，支持智能对话、API搜索、系统管理等功能。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **内容类型**: `application/json`
- **认证**: 暂无（后续可添加）

## API接口

### 1. 健康检查

#### GET /health

检查服务健康状态。

**响应示例**:
```json
{
  "status": "healthy",
  "message": "RestAgent is running"
}
```

### 2. 聊天接口

#### POST /api/chat/send

发送聊天消息，返回流式响应。

**请求体**:
```json
{
  "message": "帮我搜索用户相关的API",
  "history": [
    {"role": "user", "content": "之前的用户消息"},
    {"role": "assistant", "content": "之前的助手回复"}
  ],
  "max_iterations": 5
}
```

**响应**: 流式文本，每行格式为 `data: {json}`

**响应示例**:
```
data: {"type": "search", "message": "正在搜索相关API..."}
data: {"type": "tools_found", "message": "找到 3 个相关API工具", "tools": ["get_user_info", "create_user"]}
data: {"type": "response", "message": "我找到了以下用户相关的API接口..."}
data: [DONE]
```

#### GET /api/chat/status

获取聊天系统状态。

**响应示例**:
```json
{
  "status": "ready",
  "database": {
    "status": "connected",
    "api_count": 25
  }
}
```

### 3. API搜索接口

#### POST /api/search/vector

向量搜索API接口。

**请求体**:
```json
{
  "query": "用户登录",
  "n_results": 10
}
```

**响应示例**:
```json
{
  "success": true,
  "query": "用户登录",
  "results": [
    {
      "api_id": "123",
      "title": "用户登录",
      "path": "/api/auth/login",
      "method": "POST",
      "category": "认证",
      "similarity_score": 0.95,
      "document": "用户登录接口，支持邮箱和手机号登录"
    }
  ],
  "count": 1
}
```

#### GET /api/search/keyword

关键词搜索API接口。

**查询参数**:
- `q`: 搜索关键词（必需）
- `project_id`: 项目ID（可选）

**响应格式**: 与向量搜索相同

#### GET /api/search/detail/{api_id}

获取API详细信息。

**路径参数**:
- `api_id`: API ID

**响应示例**:
```json
{
  "success": true,
  "api": {
    "_id": "123",
    "title": "用户登录",
    "path": "/api/auth/login",
    "method": "POST",
    "desc": "用户登录接口",
    "req_query": [],
    "req_body_other": "{\"email\": \"string\", \"password\": \"string\"}",
    "res_body": "{\"token\": \"string\", \"user\": {}}"
  }
}
```

#### POST /api/search/call

调用API接口。

**请求体**:
```json
{
  "api_id": "123",
  "parameters": {
    "email": "<EMAIL>",
    "password": "password123"
  },
  "base_url": "https://api.example.com"
}
```

**响应示例**:
```json
{
  "success": true,
  "api_id": "123",
  "parameters": {...},
  "result": {
    "success": true,
    "status_code": 200,
    "headers": {...},
    "response": {
      "token": "jwt_token_here",
      "user": {...}
    }
  }
}
```

### 4. 系统管理接口

#### POST /api/admin/sync

同步API数据到向量数据库。

**请求体**:
```json
{
  "project_id": "optional_project_id"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "API数据同步成功",
  "database_info": {
    "status": "connected",
    "api_count": 30
  }
}
```

#### GET /api/admin/database/info

获取数据库信息。

**响应示例**:
```json
{
  "success": true,
  "database_info": {
    "status": "connected",
    "collection_name": "rest_apis",
    "api_count": 25,
    "persist_directory": "./data/chroma_db"
  }
}
```

#### DELETE /api/admin/database/clear

清空向量数据库。

**响应示例**:
```json
{
  "success": true,
  "message": "向量数据库已清空"
}
```

#### GET /api/admin/project/info

获取YApi项目信息。

**响应示例**:
```json
{
  "success": true,
  "project": {
    "_id": "123",
    "name": "示例项目",
    "desc": "项目描述",
    "add_time": 1640995200
  }
}
```

#### GET /api/admin/logs

获取系统日志。

**查询参数**:
- `lines`: 日志行数（默认100）
- `level`: 日志级别（可选）

**响应示例**:
```json
{
  "success": true,
  "logs": [
    "2024-01-01 12:00:00 | INFO | 应用启动成功",
    "2024-01-01 12:01:00 | DEBUG | 处理用户请求"
  ],
  "total_lines": 1000,
  "returned_lines": 100
}
```

### 5. 配置管理接口

#### GET /api/config/

获取系统配置（敏感信息已隐藏）。

**响应示例**:
```json
{
  "success": true,
  "config": {
    "yapi": {
      "base_url": "http://localhost:3000",
      "username": "admin",
      "project_id": "123",
      "password": "***"
    },
    "llm": {
      "provider": "openai",
      "model_name": "gpt-3.5-turbo",
      "api_key": "***"
    }
  }
}
```

#### POST /api/config/test/yapi

测试YApi连接。

**响应示例**:
```json
{
  "success": true,
  "message": "YApi连接成功",
  "project": {
    "name": "示例项目",
    "id": "123"
  }
}
```

#### POST /api/config/test/llm

测试大模型连接。

**响应示例**:
```json
{
  "success": true,
  "message": "大模型连接成功",
  "model": "gpt-3.5-turbo",
  "response": "Hello! This is a test response..."
}
```

## 错误处理

所有API接口在发生错误时返回统一格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### Python示例

```python
import requests

# 发送聊天消息
response = requests.post('http://localhost:8000/api/chat/send', 
    json={
        'message': '帮我搜索用户API',
        'history': []
    },
    stream=True
)

for line in response.iter_lines():
    if line.startswith(b'data: '):
        data = line[6:].decode('utf-8')
        if data != '[DONE]':
            print(json.loads(data))

# 搜索API
response = requests.post('http://localhost:8000/api/search/vector',
    json={
        'query': '用户登录',
        'n_results': 5
    }
)
print(response.json())
```

### JavaScript示例

```javascript
// 发送聊天消息
const response = await fetch('/api/chat/send', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        message: '帮我搜索用户API',
        history: []
    })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');
    
    for (const line of lines) {
        if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data !== '[DONE]') {
                console.log(JSON.parse(data));
            }
        }
    }
}

// 搜索API
const searchResponse = await fetch('/api/search/vector', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        query: '用户登录',
        n_results: 5
    })
});

const searchData = await searchResponse.json();
console.log(searchData);
```

## 限制和注意事项

1. **流式响应**: 聊天接口使用流式响应，需要特殊处理
2. **API调用**: 调用外部API时需要正确的认证信息
3. **数据同步**: 首次使用前需要同步YApi数据
4. **配置要求**: 需要正确配置YApi、大模型等服务信息
