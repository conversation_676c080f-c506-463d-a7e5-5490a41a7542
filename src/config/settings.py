"""
配置管理模块
"""

import os
import yaml
from typing import Dict, Any, Optional
from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings
from functools import lru_cache


class YApiConfig(BaseModel):
    """YApi配置"""
    base_url: str = Field(default="http://localhost:3000")
    username: str = Field(default="")
    password: str = Field(default="")
    project_id: str = Field(default="")


class LLMConfig(BaseModel):
    """大模型配置"""
    provider: str = Field(default="openai")
    base_url: str = Field(default="https://api.openai.com/v1")
    api_key: str = Field(default="")
    model_name: str = Field(default="gpt-3.5-turbo")
    temperature: float = Field(default=0.7)
    max_tokens: int = Field(default=2000)


class EmbeddingConfig(BaseModel):
    """嵌入模型配置"""
    provider: str = Field(default="openai")
    base_url: str = Field(default="https://api.openai.com/v1")
    api_key: str = Field(default="")
    model_name: str = Field(default="text-embedding-ada-002")


class VectorDBConfig(BaseModel):
    """向量数据库配置"""
    type: str = Field(default="chromadb")
    persist_directory: str = Field(default="./data/chroma_db")
    collection_name: str = Field(default="rest_apis")


class RestAuthConfig(BaseModel):
    """REST服务授权配置"""
    default_headers: Dict[str, str] = Field(default_factory=lambda: {"Content-Type": "application/json"})
    services: Dict[str, Dict[str, Any]] = Field(default_factory=dict)


class WebConfig(BaseModel):
    """Web配置"""
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    debug: bool = Field(default=True)


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO")
    file: str = Field(default="./logs/app.log")


class Settings(BaseSettings):
    """应用设置"""
    yapi: YApiConfig = Field(default_factory=YApiConfig)
    llm: LLMConfig = Field(default_factory=LLMConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    vector_db: VectorDBConfig = Field(default_factory=VectorDBConfig)
    rest_auth: RestAuthConfig = Field(default_factory=RestAuthConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @classmethod
    def from_yaml(cls, config_path: str = "config.yaml") -> "Settings":
        """从YAML文件加载配置"""
        if not os.path.exists(config_path):
            return cls()
        
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)


@lru_cache()
def get_settings() -> Settings:
    """获取应用设置（单例模式）"""
    return Settings.from_yaml()
