"""
YApi管理器
"""

from typing import List, Dict, Any, Optional
from loguru import logger
from .client import YApiClient
from .parser import YApiParser


class YApiManager:
    """YApi管理器"""
    
    def __init__(self):
        self.client = YApiClient()
        self.parser = YApiParser()
    
    def sync_project_apis(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        同步项目API数据
        
        Args:
            project_id: 项目ID
            
        Returns:
            List[Dict]: 解析后的API列表
        """
        try:
            logger.info("开始同步项目API数据")
            
            # 获取原始API数据
            raw_apis = self.client.get_all_project_interfaces(project_id)
            if not raw_apis:
                logger.warning("未获取到任何API数据")
                return []
            
            # 解析API数据
            parsed_apis = []
            for raw_api in raw_apis:
                try:
                    parsed_api = self.parser.parse_interface(raw_api)
                    parsed_apis.append(parsed_api)
                except Exception as e:
                    logger.error(f"解析API失败: {e}")
                    continue
            
            logger.info(f"成功同步 {len(parsed_apis)} 个API")
            return parsed_apis
            
        except Exception as e:
            logger.error(f"同步项目API数据失败: {e}")
            return []
    
    def get_project_info(self, project_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取项目信息"""
        try:
            return self.client.get_project_info(project_id)
        except Exception as e:
            logger.error(f"获取项目信息失败: {e}")
            return None
    
    def get_api_detail(self, api_id: str) -> Optional[Dict[str, Any]]:
        """
        获取API详细信息
        
        Args:
            api_id: API ID
            
        Returns:
            Optional[Dict]: API详细信息
        """
        try:
            raw_api = self.client.get_interface_detail(api_id)
            if raw_api:
                return self.parser.parse_interface(raw_api)
            return None
            
        except Exception as e:
            logger.error(f"获取API详细信息失败: {e}")
            return None
    
    def generate_api_curl(self, api_data: Dict[str, Any], base_url: str = "") -> str:
        """
        生成API的curl命令
        
        Args:
            api_data: API数据
            base_url: 基础URL
            
        Returns:
            str: curl命令
        """
        try:
            return self.parser.generate_curl_command(api_data, base_url)
        except Exception as e:
            logger.error(f"生成curl命令失败: {e}")
            return ""
    
    def search_apis_by_keyword(self, keyword: str, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据关键词搜索API
        
        Args:
            keyword: 搜索关键词
            project_id: 项目ID
            
        Returns:
            List[Dict]: 匹配的API列表
        """
        try:
            logger.info(f"搜索关键词: {keyword}")
            
            # 获取所有API
            all_apis = self.sync_project_apis(project_id)
            if not all_apis:
                return []
            
            # 简单的关键词匹配
            keyword_lower = keyword.lower()
            matched_apis = []
            
            for api in all_apis:
                # 在标题、路径、描述中搜索
                title = api.get("title", "").lower()
                path = api.get("path", "").lower()
                desc = api.get("desc", "").lower()
                catname = api.get("catname", "").lower()
                
                if (keyword_lower in title or 
                    keyword_lower in path or 
                    keyword_lower in desc or 
                    keyword_lower in catname):
                    matched_apis.append(api)
            
            logger.info(f"找到 {len(matched_apis)} 个匹配的API")
            return matched_apis
            
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def get_project_statistics(self, project_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取项目统计信息
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 统计信息
        """
        try:
            apis = self.sync_project_apis(project_id)
            if not apis:
                return {
                    "total_apis": 0,
                    "methods": {},
                    "categories": {},
                    "status": {}
                }
            
            # 统计方法分布
            methods = {}
            for api in apis:
                method = api.get("method", "").upper()
                methods[method] = methods.get(method, 0) + 1
            
            # 统计分类分布
            categories = {}
            for api in apis:
                catname = api.get("catname", "未分类")
                categories[catname] = categories.get(catname, 0) + 1
            
            # 统计状态分布
            status = {}
            for api in apis:
                api_status = api.get("status", "undone")
                status[api_status] = status.get(api_status, 0) + 1
            
            return {
                "total_apis": len(apis),
                "methods": methods,
                "categories": categories,
                "status": status
            }
            
        except Exception as e:
            logger.error(f"获取项目统计信息失败: {e}")
            return {}
    
    def close(self):
        """关闭管理器"""
        if self.client:
            self.client.close()
