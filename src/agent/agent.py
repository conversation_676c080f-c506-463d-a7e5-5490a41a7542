"""
智能助手Agent核心类
"""

import json
from typing import List, Dict, Any, Optional, Generator
from loguru import logger
import asyncio
from ..vector_db import VectorDBManager
from ..yapi_client import YApiManager
from .llm_client import LLMClient
from .api_caller import ApiCaller


class RestAgent:
    """REST API智能助手"""
    
    def __init__(self):
        self.vector_db = VectorDBManager()
        self.yapi_manager = YApiManager()
        self.llm_client = LLMClient()
        self.api_caller = ApiCaller()
        
        # 系统提示词
        self.system_prompt = """你是一个专业的REST API调用助手。你的任务是：

1. 理解用户的需求
2. 从可用的API工具中选择最合适的接口
3. 根据用户输入构造正确的API调用参数
4. 执行API调用并解释结果

当用户提出请求时，你应该：
- 分析用户需求，确定需要调用哪个API
- 如果需要参数，向用户询问必要的信息
- 调用相应的API工具
- 解释API返回的结果，用通俗易懂的语言回答用户

请始终保持专业和友好的态度。"""
    
    def chat(
        self,
        user_input: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        max_iterations: int = 5
    ) -> Generator[Dict[str, Any], None, None]:
        """
        与用户对话
        
        Args:
            user_input: 用户输入
            conversation_history: 对话历史
            max_iterations: 最大迭代次数
            
        Yields:
            Dict: 对话步骤结果
        """
        try:
            logger.info(f"用户输入: {user_input}")
            
            # 初始化对话历史
            if conversation_history is None:
                conversation_history = []
            
            # 添加系统提示词
            messages = [{"role": "system", "content": self.system_prompt}]
            messages.extend(conversation_history)
            messages.append({"role": "user", "content": user_input})
            
            # 搜索相关API
            yield {"type": "search", "message": "正在搜索相关API..."}
            relevant_apis = self.vector_db.search_apis(user_input, n_results=5)
            
            if not relevant_apis:
                yield {
                    "type": "response",
                    "message": "抱歉，我没有找到相关的API接口来处理您的请求。",
                    "apis": []
                }
                return
            
            # 构建工具定义
            tools = []
            api_map = {}
            
            for api in relevant_apis:
                # 获取完整的API数据
                api_id = api.get("api_id")
                if api_id:
                    full_api_data = self.yapi_manager.get_api_detail(api_id)
                    if full_api_data:
                        tool_def = self.llm_client.create_tool_definition(full_api_data)
                        if tool_def:
                            tools.append(tool_def)
                            tool_name = tool_def["function"]["name"]
                            api_map[tool_name] = full_api_data
            
            if not tools:
                yield {
                    "type": "response",
                    "message": "找到了相关的API，但无法创建工具定义。",
                    "apis": relevant_apis
                }
                return
            
            yield {
                "type": "tools_found",
                "message": f"找到 {len(tools)} 个相关API工具",
                "tools": [tool["function"]["name"] for tool in tools]
            }
            
            # 开始对话循环
            iteration = 0
            while iteration < max_iterations:
                iteration += 1
                
                yield {"type": "thinking", "message": f"正在思考... (第{iteration}轮)"}
                
                # 调用大模型
                result = asyncio.run(self.llm_client.chat_completion(
                    messages=messages,
                    tools=tools,
                    tool_choice="auto"
                ))
                
                if "error" in result:
                    yield {
                        "type": "error",
                        "message": f"大模型调用失败: {result['error']}"
                    }
                    return
                
                if not result.get("content") and not result.get("tool_calls"):
                    yield {
                        "type": "error",
                        "message": "大模型没有返回有效响应"
                    }
                    return
                
                if result.get("content"):
                    content = {
                        "role": "assistant",
                        "content": result.get("content")
                    }
                    
                    # 添加助手消息到对话历史
                    messages.append(content)
                
                # 检查是否有工具调用
                tool_calls = result.get("tool_calls", [])
                
                if not tool_calls:
                    # 没有工具调用，返回最终回答
                    yield {
                        "type": "response",
                        "message": result.get("content"),
                        #"apis": relevant_apis
                    }
                    return
                
                # 执行工具调用
                for tool_call in tool_calls:
                    yield {
                        "type": "tool_call",
                        "message": f"正在调用API: {tool_call['function']['name']}",
                        "tool_name": tool_call['function']['name']
                    }
                    
                    tool_result = self._execute_tool_call(tool_call, api_map)
                    
                    # 添加工具调用结果到消息
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call["id"],
                        "content": json.dumps(tool_result, ensure_ascii=False)
                    })
                    
                    yield {
                        "type": "tool_result",
                        "message": "API调用完成",
                        "tool_name": tool_call['function']['name'],
                        "result": tool_result
                    }
            
            # 如果达到最大迭代次数
            yield {
                "type": "response",
                "message": "对话已达到最大轮次，请重新开始。"
            }
            
        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            yield {
                "type": "error",
                "message": f"处理请求时发生错误: {str(e)}"
            }
    
    def _execute_tool_call(
        self,
        tool_call: Dict[str, Any],
        api_map: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        执行工具调用
        
        Args:
            tool_call: 工具调用信息
            api_map: API映射
            
        Returns:
            Dict: 执行结果
        """
        try:
            tool_name = tool_call["function"]["name"]
            arguments_str = tool_call["function"]["arguments"]
            
            # 解析参数
            try:
                # 处理特殊情况，例如已转义的双引号 "\'"
                arguments_str = arguments_str.replace('\\"', '"').replace("\\'", "'")
                
                # 将单引号替换为双引号
                arguments_str = arguments_str.replace("'", '"')
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "error": f"参数解析失败: {e}",
                    "arguments": arguments_str
                }
            
            # 获取API数据
            api_data = api_map.get(tool_name)
            if not api_data:
                return {
                    "success": False,
                    "error": f"未找到工具 {tool_name} 对应的API数据"
                }
            
            logger.info(f"执行工具调用: {tool_name}, 参数: {arguments}")
            
            # 调用API
            result = self.api_caller.call_api(api_data, arguments)
            
            return result
            
        except Exception as e:
            logger.error(f"执行工具调用失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def search_apis(self, query: str, n_results: int = 10) -> List[Dict[str, Any]]:
        """
        搜索API
        
        Args:
            query: 搜索查询
            n_results: 结果数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            return self.vector_db.search_apis(query, n_results)
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def sync_apis(self, project_id: Optional[str] = None) -> bool:
        """
        同步API数据到向量数据库
        
        Args:
            project_id: 项目ID
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始同步API数据")
            
            # 从YApi获取API数据
            apis = self.yapi_manager.sync_project_apis(project_id)
            if not apis:
                logger.warning("没有获取到API数据")
                return False
            
            # 添加到向量数据库
            success = self.vector_db.add_apis(apis)
            
            if success:
                logger.info(f"成功同步 {len(apis)} 个API到向量数据库")
            else:
                logger.error("同步API到向量数据库失败")
            
            return success
            
        except Exception as e:
            logger.error(f"同步API数据失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        return self.vector_db.get_database_info()
    
    def clear_database(self) -> bool:
        """清空数据库"""
        return self.vector_db.clear_database()
