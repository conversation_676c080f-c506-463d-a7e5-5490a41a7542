"""
向量数据库管理器
"""

from typing import List, Dict, Any, Optional
from loguru import logger
from .chroma_client import ChromaDBClient
from .embedding_client import EmbeddingClient


class VectorDBManager:
    """向量数据库管理器"""
    
    def __init__(self):
        self.chroma_client = ChromaDBClient()
        self.embedding_client = EmbeddingClient()
    
    def add_apis(self, apis: List[Dict[str, Any]]) -> bool:
        """
        添加API到向量数据库

        Args:
            apis: API列表

        Returns:
            bool: 是否成功
        """
        try:
            if not apis:
                logger.warning("没有API需要添加")
                return True

            logger.info(f"开始添加 {len(apis)} 个API到向量数据库")

            # 构建文档内容用于向量化
            documents = []
            for api in apis:
                doc_content = self.chroma_client._build_document_content(api)
                documents.append(doc_content)

            # 分批处理嵌入向量生成（避免token限制）
            logger.info("正在生成文档向量...")
            batch_size = 8  # 减少批处理大小以避免token限制
            all_embeddings = []

            # 截断文档长度以避免token过多
            max_doc_length = 500  # 限制每个文档的字符数
            truncated_docs = []
            for doc in documents:
                if len(doc) > max_doc_length:
                    truncated_doc = doc[:max_doc_length] + "..."
                    truncated_docs.append(truncated_doc)
                else:
                    truncated_docs.append(doc)

            for i in range(0, len(truncated_docs), batch_size):
                batch_docs = truncated_docs[i:i + batch_size]
                logger.info(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch_docs)} 个文档")

                batch_embeddings = self.embedding_client.get_embeddings(batch_docs)
                if not batch_embeddings:
                    logger.error(f"第 {i//batch_size + 1} 批嵌入向量生成失败")
                    return False

                all_embeddings.extend(batch_embeddings)

            if len(all_embeddings) != len(apis):
                logger.error(f"嵌入向量数量不匹配: {len(all_embeddings)} != {len(apis)}")
                return False

            # 添加到ChromaDB，使用我们生成的嵌入向量
            success = self.chroma_client.add_api_documents(apis, all_embeddings)

            if success:
                logger.info(f"成功添加 {len(apis)} 个API到向量数据库")
            else:
                logger.error("添加API到向量数据库失败")

            return success

        except Exception as e:
            logger.error(f"添加API到向量数据库失败: {e}")
            return False
    
    def search_apis(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关API

        Args:
            query: 搜索查询
            n_results: 返回结果数量

        Returns:
            List[Dict]: 搜索结果
        """
        try:
            logger.info(f"搜索API: {query}")

            # 使用嵌入模型将查询向量化
            logger.debug("正在生成查询向量...")
            query_embedding = self.embedding_client.get_single_embedding(query)

            if not query_embedding:
                logger.error("生成查询向量失败，使用文本搜索")
                # 如果向量化失败，回退到文本搜索
                results = self.chroma_client.search_apis(query, n_results)
            else:
                # 使用向量搜索
                results = self.chroma_client.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=n_results,
                    include=["documents", "metadatas", "distances"]
                )

                # 转换为统一格式
                search_results = []
                if results["documents"] and results["documents"][0]:
                    for i, doc in enumerate(results["documents"][0]):
                        result = {
                            "document": doc,
                            "metadata": results["metadatas"][0][i],
                            "distance": results["distances"][0][i]
                        }
                        search_results.append(result)
                results = search_results

            # 格式化结果
            formatted_results = []
            for result in results:
                metadata = result.get("metadata", {})
                formatted_result = {
                    "api_id": metadata.get("api_id", ""),
                    "title": metadata.get("title", ""),
                    "path": metadata.get("path", ""),
                    "method": metadata.get("method", ""),
                    "category": metadata.get("category", ""),
                    "status": metadata.get("status", ""),
                    "document": result.get("document", ""),
                    "similarity_score": 1 - result.get("distance", 1),  # 转换为相似度分数
                    "metadata": metadata
                }
                formatted_results.append(formatted_result)

            logger.info(f"找到 {len(formatted_results)} 个相关API")
            return formatted_results

        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            info = self.chroma_client.get_collection_info()
            return {
                "status": "connected",
                "collection_name": info.get("name", ""),
                "api_count": info.get("count", 0),
                "persist_directory": info.get("persist_directory", "")
            }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def clear_database(self) -> bool:
        """清空数据库"""
        try:
            logger.info("开始清空向量数据库")
            success = self.chroma_client.clear_collection()
            
            if success:
                logger.info("成功清空向量数据库")
            else:
                logger.error("清空向量数据库失败")
            
            return success
            
        except Exception as e:
            logger.error(f"清空向量数据库失败: {e}")
            return False
    
    def batch_search_apis(self, queries: List[str], n_results: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量搜索API
        
        Args:
            queries: 查询列表
            n_results: 每个查询返回的结果数量
            
        Returns:
            Dict[str, List[Dict]]: 查询结果字典
        """
        try:
            results = {}
            for query in queries:
                results[query] = self.search_apis(query, n_results)
            
            return results
            
        except Exception as e:
            logger.error(f"批量搜索API失败: {e}")
            return {}
    
    def get_api_by_id(self, api_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取API信息
        
        Args:
            api_id: API ID
            
        Returns:
            Optional[Dict]: API信息
        """
        try:
            # 使用精确匹配搜索
            results = self.chroma_client.collection.get(
                ids=[api_id],
                include=["documents", "metadatas"]
            )
            
            if results["documents"] and results["documents"][0]:
                metadata = results["metadatas"][0]
                return {
                    "api_id": metadata.get("api_id", ""),
                    "title": metadata.get("title", ""),
                    "path": metadata.get("path", ""),
                    "method": metadata.get("method", ""),
                    "category": metadata.get("category", ""),
                    "status": metadata.get("status", ""),
                    "document": results["documents"][0],
                    "metadata": metadata
                }
            
            return None
            
        except Exception as e:
            logger.error(f"根据ID获取API失败: {e}")
            return None
