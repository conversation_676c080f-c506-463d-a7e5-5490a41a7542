# RestAgent 部署指南

## 系统要求

- Python 3.8+
- 内存: 最少2GB，推荐4GB+
- 磁盘: 最少1GB可用空间
- 网络: 需要访问YApi服务和大模型API

## 快速部署

### 1. 下载代码

```bash
git clone <repository_url>
cd restAgent
```

### 2. 安装依赖

```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制配置文件
cp .env.example .env
cp config.yaml config.yaml

# 编辑配置文件
nano .env
nano config.yaml
```

### 4. 启动服务

```bash
python run.py
```

访问 http://localhost:8000 查看应用。

## 详细配置

### 1. 环境变量配置 (.env)

```bash
# YApi配置
YAPI_BASE_URL=http://your-yapi-server:3000
YAPI_USERNAME=your_username
YAPI_PASSWORD=your_password
YAPI_PROJECT_ID=your_project_id

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 其他配置
DEBUG=false
```

### 2. 主配置文件 (config.yaml)

```yaml
# YApi配置
yapi:
  base_url: "http://your-yapi-server:3000"
  username: "your_username"
  password: "your_password"
  project_id: "your_project_id"

# 大模型配置
llm:
  provider: "openai"
  base_url: "https://api.openai.com/v1"
  api_key: "your_openai_api_key"
  model_name: "gpt-3.5-turbo"
  temperature: 0.7
  max_tokens: 2000

# 嵌入模型配置
embedding:
  provider: "openai"
  base_url: "https://api.openai.com/v1"
  api_key: "your_openai_api_key"
  model_name: "text-embedding-ada-002"

# 向量数据库配置
vector_db:
  type: "chromadb"
  persist_directory: "./data/chroma_db"
  collection_name: "rest_apis"

# Web服务配置
web:
  host: "0.0.0.0"
  port: 8000
  debug: false

# 日志配置
logging:
  level: "INFO"
  file: "./logs/app.log"
```

## Docker部署

### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs data/chroma_db

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "run.py", "--host", "0.0.0.0"]
```

### 2. 构建和运行

```bash
# 构建镜像
docker build -t restagent .

# 运行容器
docker run -d \
  --name restagent \
  -p 8000:8000 \
  -v $(pwd)/config.yaml:/app/config.yaml \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  restagent
```

### 3. Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  restagent:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./.env:/app/.env
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
```

运行：

```bash
docker-compose up -d
```

## 生产环境部署

### 1. 使用Nginx反向代理

创建 `/etc/nginx/sites-available/restagent`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持流式响应
        proxy_buffering off;
        proxy_cache off;
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/restagent /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2. 使用Systemd服务

创建 `/etc/systemd/system/restagent.service`:

```ini
[Unit]
Description=RestAgent Service
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/restAgent
Environment=PATH=/path/to/restAgent/venv/bin
ExecStart=/path/to/restAgent/venv/bin/python /path/to/restAgent/run.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable restagent
sudo systemctl start restagent
sudo systemctl status restagent
```

### 3. 使用Supervisor

安装Supervisor：

```bash
sudo apt-get install supervisor
```

创建 `/etc/supervisor/conf.d/restagent.conf`:

```ini
[program:restagent]
command=/path/to/restAgent/venv/bin/python /path/to/restAgent/run.py
directory=/path/to/restAgent
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/restagent.log
```

重新加载配置：

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start restagent
```

## 性能优化

### 1. 数据库优化

- 定期清理向量数据库
- 调整ChromaDB的配置参数
- 使用SSD存储提高I/O性能

### 2. 应用优化

- 启用生产模式（debug=false）
- 调整日志级别为INFO或WARNING
- 使用连接池管理HTTP连接

### 3. 系统优化

- 增加文件描述符限制
- 调整内核参数
- 使用负载均衡器分发请求

## 监控和维护

### 1. 日志监控

```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 日志轮转
logrotate /etc/logrotate.d/restagent
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查系统资源
htop
df -h
```

### 3. 备份策略

```bash
# 备份配置文件
cp config.yaml config.yaml.backup
cp .env .env.backup

# 备份向量数据库
tar -czf data_backup_$(date +%Y%m%d).tar.gz data/

# 备份日志
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

## 故障排除

### 1. 常见问题

**服务无法启动**:
- 检查端口是否被占用
- 验证配置文件格式
- 查看错误日志

**YApi连接失败**:
- 检查网络连接
- 验证认证信息
- 确认YApi服务状态

**大模型调用失败**:
- 检查API密钥
- 验证网络连接
- 确认配额限制

**向量数据库错误**:
- 检查磁盘空间
- 验证目录权限
- 重建数据库索引

### 2. 调试命令

```bash
# 检查进程
ps aux | grep python

# 检查端口
netstat -tlnp | grep 8000

# 检查日志
journalctl -u restagent -f

# 测试配置
python -c "from src.config.settings import get_settings; print(get_settings())"
```

### 3. 性能分析

```bash
# 内存使用
free -h

# CPU使用
top -p $(pgrep -f restagent)

# 磁盘I/O
iotop

# 网络连接
ss -tulpn | grep 8000
```

## 安全建议

1. **网络安全**:
   - 使用HTTPS
   - 配置防火墙
   - 限制访问IP

2. **应用安全**:
   - 定期更新依赖
   - 使用强密码
   - 启用访问日志

3. **数据安全**:
   - 定期备份数据
   - 加密敏感配置
   - 限制文件权限

## 扩展部署

### 1. 多实例部署

使用负载均衡器部署多个实例：

```bash
# 启动多个实例
python run.py --port 8001 &
python run.py --port 8002 &
python run.py --port 8003 &
```

### 2. 微服务架构

将不同模块拆分为独立服务：

- API网关服务
- 向量搜索服务
- 大模型调用服务
- YApi代理服务

### 3. 云原生部署

使用Kubernetes部署：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: restagent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: restagent
  template:
    metadata:
      labels:
        app: restagent
    spec:
      containers:
      - name: restagent
        image: restagent:latest
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONPATH
          value: "/app"
```
