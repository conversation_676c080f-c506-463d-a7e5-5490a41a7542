# RestAgent 配置文件

# YApi 配置
yapi:
  base_url: "http://************:3000/"  # YApi服务地址
  username: "<EMAIL>"  # YApi用户名
  password: "jiadx@2025"  # YApi密码
  project_id: "46"  # 项目ID

# 大模型配置
llm:
  provider: "openai"  # 支持 openai, azure, local
  base_url: "https://ai.secsign.online:3003/v1"
  api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  model_name: "qwen3-32b"
  temperature: 0.3
  max_tokens: 20000

# 嵌入模型配置
embedding:
  provider: "openai"
  base_url: "https://ai.secsign.online:38080"
  api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  model_name: "embed"

# 向量数据库配置
vector_db:
  type: "chromadb"
  persist_directory: "./data/chroma_db"
  collection_name: "rest_apis"

# REST服务授权配置
rest_auth:
  default_headers:
    "Content-Type": "application/json"
  # 可以配置多个服务的授权信息
  services:
    "46":
      base_url: "https://************:18081/crm"
      headers:
        "Authorization": "Bearer your_token_here"

# Web UI配置
web:
  host: "0.0.0.0"
  port: 8000
  debug: true

# 日志配置
logging:
  level: "INFO"
  file: "./logs/app.log"
