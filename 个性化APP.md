# 功能描述
是能实现，中间不用询问，继续完成全部任务并测试验证：

性化APP定制功能，用户可以使用后台rest服务api，定制自己个性化的app界面，具体要求如下：
1. 增加一个“个性化APP”入口菜单，主界面类似应用商城，可显示当前已经创建的各APP，包括名称、描述、图标，用户点击APP可跳转到这个APP界面。
2. 可以通过“创建APP”启动APP的构建界面：
    2.1 用户可以输入请求，后台调用大模型根据用户请求生成前端界面，生成的前端界面通过REST API接口查询、提交数据。
    2.2 调用大模型时，可以把API向量库的搜索能力做为工具告诉大模型，让大模型在生成界面时，可以调用向量数据库进行搜索有哪些API可用。
    2.3 APP构建过程支持迭代， 用户可以在请求框提交新的要求，让大模型根据要求修改、输出新的APP界面。
    2.4 APP构建界面可分为左右2部分，左边是用户和大模型的交互记录，右边可以直接预览和编辑当前生成的APP界面，并且支持在线修改后刷新预览。左边交互记录部分和右边预览/编辑部分都可以分别隐藏。
    2.5 创建的APP，保存到data/html目录下，并且能够通过“个性化APP”主界面的链接访问。
    2.6 已经创建的APP，支持再次修改。